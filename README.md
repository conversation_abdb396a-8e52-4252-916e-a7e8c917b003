# 留言墙系统

一个完整的留言墙系统，包含前后端功能，支持用户注册登录、留言发布、支付购买、敏感词过滤和管理后台。

## 功能特性

- 用户注册登录
- 留言发布与管理
- 支付购买留言包
- 敏感词自动过滤
- 管理后台
  - 留言审核
  - 敏感词管理
  - 支付订单查看
  - 操作日志记录

## 技术栈

- 前端：Vue 3 + Vite + Tailwind CSS
- 后端：Node.js + Express + MySQL
- 支付：集成微信支付回调

## 快速开始

1. 安装依赖
```bash
cd backend && npm install
cd ../frontend && npm install
```

2. 初始化数据库
```bash
mysql -u root -p < backend/init_db.sql
```

3. 配置环境变量
复制`.env.example`为`.env`并修改配置

4. 启动服务
```bash
# 后端
cd backend && npm start

# 前端
cd frontend && npm run dev
```

## 管理员账号
- 用户名: admin
- 密码: admin123

## API文档
[查看API文档](docs/api.md)

## 项目结构
```
backend/          # 后端代码
  ├── middleware/ # 中间件
  ├── routes/     # 路由
  ├── app.js      # 主应用
  └── init_db.sql # 数据库初始化

frontend/        # 前端代码
  ├── src/       # 源代码
  └── vite.config.js # 构建配置
```
