const { pool } = require('../app')

module.exports = async (req, res, next) => {
  try {
    const userId = req.user?.userId || null
    const action = `${req.method} ${req.originalUrl}`
    const ip = req.ip || req.connection.remoteAddress
    
    await pool.execute(
      `INSERT INTO operation_logs 
      (user_id, action, ip, user_agent, created_at) 
      VALUES (?, ?, ?, ?, NOW())`,
      [userId, action, ip, req.get('User-Agent')]
    )
    
    next()
  } catch (err) {
    console.error('日志记录失败:', err)
    next()
  }
}
