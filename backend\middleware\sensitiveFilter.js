const pool = require('../db')

module.exports = async (req, res, next) => {
  try {
    // 只过滤POST/PUT请求的文本内容
    if (!['POST', 'PUT'].includes(req.method)) {
      return next()
    }

    // 获取所有敏感词
    const [words] = await pool.execute(
      'SELECT word FROM sensitive_words'
    )
    const sensitiveWords = words.map(w => w.word)

    // 过滤请求体中的文本字段
    const filterText = (text) => {
      if (!text || typeof text !== 'string') return text
      
      let filtered = text
      sensitiveWords.forEach(word => {
        const regex = new RegExp(word, 'gi')
        filtered = filtered.replace(regex, '*'.repeat(word.length))
      })
      return filtered
    }

    // 处理普通对象
    if (req.body && typeof req.body === 'object') {
      for (const key in req.body) {
        if (typeof req.body[key] === 'string') {
          req.body[key] = filterText(req.body[key])
        }
      }
    }

    next()
  } catch (err) {
    console.error('敏感词过滤错误:', err)
    next()
  }
}
