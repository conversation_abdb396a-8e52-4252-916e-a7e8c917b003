const axios = require('axios')
const crypto = require('crypto')
const db = require('../db')

class WeChatPay {
  constructor() {
    this.config = {}
    this.loadConfig()
  }

  async loadConfig() {
    try {
      const [rows] = await db.query('SELECT * FROM wechat_pay_config LIMIT 1')
      if (rows.length > 0) {
        this.config = rows[0]
      }
    } catch (err) {
      console.error('加载微信支付配置失败:', err)
    }
  }

  // 生成签名
  generateSign(params) {
    const sortedParams = Object.keys(params)
      .filter(key => params[key] && key !== 'sign')
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')
    
    return crypto.createHash('md5')
      .update(sortedParams + '&key=' + this.config.api_key)
      .digest('hex')
      .toUpperCase()
  }

  // 统一下单
  async createOrder(orderParams) {
    if (!this.config.mch_id || !this.config.api_key) {
      throw new Error('微信支付未配置')
    }

    const params = {
      appid: '', // 需要从微信公众平台获取
      mch_id: this.config.mch_id,
      nonce_str: Math.random().toString(36).substr(2, 32),
      ...orderParams
    }

    params.sign = this.generateSign(params)

    try {
      const url = this.config.is_live 
        ? 'https://api.mch.weixin.qq.com/pay/unifiedorder' 
        : 'https://api.mch.weixin.qq.com/sandboxnew/pay/unifiedorder'
      
      const response = await axios.post(url, this.buildXml(params), {
        headers: { 'Content-Type': 'application/xml' }
      })
      
      return this.parseXml(response.data)
    } catch (err) {
      console.error('微信支付下单失败:', err)
      throw err
    }
  }

  // 验证支付结果
  verifyPayment(params) {
    const sign = params.sign
    const localSign = this.generateSign(params)
    return sign === localSign
  }

  // 构建XML
  buildXml(params) {
    let xml = '<xml>'
    for (const key in params) {
      xml += `<${key}>${params[key]}</${key}>`
    }
    xml += '</xml>'
    return xml
  }

  // 解析XML
  parseXml(xml) {
    // 简化实现，实际项目应使用xml2js等库
    const result = {}
    const matches = xml.match(/<([^>]+)>([^<]+)<\/\1>/g)
    matches.forEach(match => {
      const key = match.match(/<([^>]+)>/)[1]
      const value = match.match(/>(.*?)</)[1]
      result[key] = value
    })
    return result
  }
}

module.exports = new WeChatPay()
