const express = require('express')
const router = express.Router()
const authenticate = require('../middleware/auth')
const pool = require('../db')

// 管理员认证中间件
const adminAuth = (req, res, next) => {
  if (!req.user || !req.user.isAdmin) {
    return res.status(403).json({ error: '无权访问' })
  }
  next()
}

// 获取待审核留言
router.get('/messages/pending', authenticate, adminAuth, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT m.*, u.username as sender_name 
      FROM messages m
      JOIN users u ON m.sender_id = u.id
      WHERE m.is_approved = false
      ORDER BY m.created_at DESC`
    )
    res.json(rows)
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 审核留言
router.put('/messages/:id/approve', authenticate, adminAuth, async (req, res) => {
  try {
    const { id } = req.params
    await pool.execute(
      'UPDATE messages SET is_approved = true WHERE id = ?',
      [id]
    )
    res.json({ message: '留言已审核通过' })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取敏感词列表
router.get('/sensitive-words', authenticate, adminAuth, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT * FROM sensitive_words ORDER BY created_at DESC'
    )
    res.json(rows)
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 添加敏感词
router.post('/sensitive-words', authenticate, adminAuth, async (req, res) => {
  try {
    const { word, category } = req.body
    const [result] = await pool.execute(
      'INSERT INTO sensitive_words (word, category) VALUES (?, ?)',
      [word, category]
    )
    res.status(201).json({ id: result.insertId })
  } catch (err) {
    if (err.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: '敏感词已存在' })
    }
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取微信支付配置
router.get('/payment/config', authenticate, adminAuth, async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM wechat_pay_config LIMIT 1')
    res.json(rows[0] || {})
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 更新微信支付配置
router.put('/payment/config', authenticate, adminAuth, async (req, res) => {
  try {
    const { mch_id, api_key, is_live } = req.body
    
    // 验证参数
    if (!mch_id || !api_key) {
      return res.status(400).json({ error: '请填写完整配置' })
    }

    const [existing] = await pool.execute('SELECT id FROM wechat_pay_config LIMIT 1')
    
    if (existing.length > 0) {
      await pool.execute(
        'UPDATE wechat_pay_config SET mch_id = ?, api_key = ?, is_live = ?',
        [mch_id, api_key, is_live]
      )
    } else {
      await pool.execute(
        'INSERT INTO wechat_pay_config (mch_id, api_key, is_live) VALUES (?, ?, ?)',
        [mch_id, api_key, is_live]
      )
    }
    
    res.json({ message: '配置更新成功' })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取留言包设置
router.get('/message-packages', authenticate, adminAuth, async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM message_packages')
    res.json(rows)
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 更新留言包设置
router.put('/message-packages/:id', authenticate, adminAuth, async (req, res) => {
  try {
    const { id } = req.params
    const { price, message_count, is_active } = req.body
    
    await pool.execute(
      'UPDATE message_packages SET price = ?, message_count = ?, is_active = ? WHERE id = ?',
      [price, message_count, is_active, id]
    )
    
    res.json({ message: '留言包更新成功' })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取付费模式设置
router.get('/payment-mode', authenticate, adminAuth, async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT config_value FROM system_config WHERE config_key = "payment_mode"'
    )
    res.json({ payment_mode: rows[0]?.config_value || 'free' })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 更新付费模式设置
router.put('/payment-mode', authenticate, adminAuth, async (req, res) => {
  try {
    const { payment_mode } = req.body
    
    if (!['free', 'paid'].includes(payment_mode)) {
      return res.status(400).json({ error: '无效的模式设置' })
    }

    await pool.execute(
      `INSERT INTO system_config (config_key, config_value, description) 
      VALUES ('payment_mode', ?, '留言发布模式：free-免费，paid-付费') 
      ON DUPLICATE KEY UPDATE config_value = ?`,
      [payment_mode, payment_mode]
    )
    
    res.json({ message: '付费模式更新成功' })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取操作日志
router.get('/logs', authenticate, adminAuth, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query
    const offset = (page - 1) * limit
    
    const [logs] = await pool.execute(
      `SELECT l.*, u.username 
      FROM operation_logs l
      LEFT JOIN users u ON l.user_id = u.id
      ORDER BY l.created_at DESC
      LIMIT ? OFFSET ?`,
      [parseInt(limit), parseInt(offset)]
    )
    
    const [total] = await pool.execute(
      'SELECT COUNT(*) as count FROM operation_logs'
    )
    
    res.json({
      logs,
      total: total[0].count,
      page: parseInt(page),
      limit: parseInt(limit)
    })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

module.exports = router
