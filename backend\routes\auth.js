const express = require('express')
const router = express.Router()
const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const pool = require('../db')
const authenticate = require('../middleware/auth')

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { username, password } = req.body

    // 检查用户名是否存在
    const [users] = await pool.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    )

    if (users.length > 0) {
      return res.status(400).json({ error: '用户名已存在' })
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 10)

    // 创建用户
    const [result] = await pool.execute(
      'INSERT INTO users (username, password) VALUES (?, ?)',
      [username, hashedPassword]
    )

    // 初始化用户积分
    await pool.execute(
      'INSERT INTO user_message_credits (user_id, credits) VALUES (?, ?)',
      [result.insertId, 0]
    )

    res.status(201).json({ message: '注册成功' })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body

    // 查询用户
    const [users] = await pool.execute(
      'SELECT * FROM users WHERE username = ?',
      [username]
    )

    if (users.length === 0) {
      return res.status(400).json({ error: '用户名或密码错误' })
    }

    const user = users[0]

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password)
    if (!isMatch) {
      return res.status(400).json({ error: '用户名或密码错误' })
    }

    // 生成Token
    const token = jwt.sign(
      { userId: user.id, username: user.username },
      process.env.JWT_SECRET || 'your_secret_key',
      { expiresIn: '7d' }
    )

    // 查询用户积分
    const [credits] = await pool.execute(
      'SELECT credits FROM user_message_credits WHERE user_id = ?',
      [user.id]
    )

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        credits: credits[0]?.credits || 0
      }
    })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取用户积分
router.get('/credits', authenticate, async (req, res) => {
  try {
    const userId = req.user.userId

    const [credits] = await pool.execute(
      'SELECT credits FROM user_message_credits WHERE user_id = ?',
      [userId]
    )

    res.json({ credits: credits[0]?.credits || 0 })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 消费积分
router.post('/credits/consume', authenticate, async (req, res) => {
  try {
    const userId = req.user.userId
    const { amount } = req.body

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: '无效的积分数量' })
    }

    // 检查积分是否足够
    const [credits] = await pool.execute(
      'SELECT credits FROM user_message_credits WHERE user_id = ? FOR UPDATE',
      [userId]
    )

    const currentCredits = credits[0]?.credits || 0
    if (currentCredits < amount) {
      return res.status(400).json({ error: '积分不足' })
    }

    // 扣除积分
    await pool.execute(
      'UPDATE user_message_credits SET credits = credits - ? WHERE user_id = ?',
      [amount, userId]
    )

    res.json({
      success: true,
      newCredits: currentCredits - amount
    })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

module.exports = router
