const express = require('express')
const router = express.Router()
const pool = require('../db')
const authenticate = require('../middleware/auth')

// 创建留言
router.post('/', authenticate, async (req, res) => {
  const connection = await pool.getConnection()
  try {
    await connection.beginTransaction()

    const { receiverName, code, content, isPublic } = req.body
    const userId = req.user.userId

    // 验证内容
    if (!receiverName || !code || !content) {
      return res.status(400).json({ error: '请填写完整信息' })
    }

    // 检查并扣除积分
    const [credits] = await connection.execute(
      'SELECT credits FROM user_message_credits WHERE user_id = ? FOR UPDATE',
      [userId]
    )

    const currentCredits = credits[0]?.credits || 0
    if (currentCredits < 1) {
      return res.status(400).json({ error: '积分不足，请先购买留言包' })
    }

    // 扣除积分
    await connection.execute(
      'UPDATE user_message_credits SET credits = credits - 1 WHERE user_id = ?',
      [userId]
    )

    // 创建留言
    const [result] = await connection.execute(
      `INSERT INTO messages
      (user_id, author_name, code, content, is_public)
      VALUES (?, ?, ?, ?, ?)`,
      [userId, receiverName, code, content, isPublic ? 1 : 0]
    )

    await connection.commit()

    res.status(201).json({
      message: '留言创建成功',
      messageId: result.insertId,
      remainingCredits: currentCredits - 1
    })
  } catch (err) {
    await connection.rollback()
    console.error('创建留言失败:', err)
    res.status(500).json({ error: '服务器错误' })
  } finally {
    connection.release()
  }
})

// 获取公开留言
router.get('/public', async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT m.*, u.username as sender_name, m.author_name as receiver_name
      FROM messages m
      LEFT JOIN users u ON m.user_id = u.id
      WHERE m.is_public = 1 AND m.is_deleted = 0
      ORDER BY m.created_at DESC
      LIMIT 10`
    )
    res.json(rows)
  } catch (err) {
    console.error('获取公开留言失败:', err)
    res.status(500).json({ error: '服务器错误' })
  }
})

// 搜索留言
router.get('/search', async (req, res) => {
  try {
    const { name, code } = req.query

    const [rows] = await pool.execute(
      `SELECT m.*, u.username as sender_name, m.author_name as receiver_name
      FROM messages m
      LEFT JOIN users u ON m.user_id = u.id
      WHERE m.author_name = ? AND m.code = ? AND m.is_deleted = 0
      ORDER BY m.created_at DESC`,
      [name, code]
    )

    if (rows.length === 0) {
      return res.status(404).json({ error: '未找到留言' })
    }

    res.json(rows)
  } catch (err) {
    console.error('搜索留言失败:', err)
    res.status(500).json({ error: '服务器错误' })
  }
})

// 获取我的留言
router.get('/my', authenticate, async (req, res) => {
  try {
    const userId = req.user.userId

    const [rows] = await pool.execute(
      `SELECT m.*, u.username as sender_name, m.author_name as receiver_name
      FROM messages m
      LEFT JOIN users u ON m.user_id = u.id
      WHERE m.user_id = ? AND m.is_deleted = 0
      ORDER BY m.created_at DESC`,
      [userId]
    )

    res.json(rows)
  } catch (err) {
    console.error('获取我的留言失败:', err)
    res.status(500).json({ error: '服务器错误' })
  }
})

// 点赞留言
router.post('/:id/like', async (req, res) => {
  try {
    const messageId = req.params.id

    // 增加点赞数
    await pool.execute(
      'UPDATE messages SET likes = likes + 1 WHERE id = ?',
      [messageId]
    )

    // 获取更新后的点赞数
    const [rows] = await pool.execute(
      'SELECT likes FROM messages WHERE id = ?',
      [messageId]
    )

    res.json({ likes: rows[0]?.likes || 0 })
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

module.exports = router
