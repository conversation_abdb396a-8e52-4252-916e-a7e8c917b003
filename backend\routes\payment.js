const express = require('express')
const router = express.Router()
const authenticate = require('../middleware/auth')
const pool = require('../db')
const wechatPay = require('../middleware/wechatPay')

// 获取留言包列表
router.get('/packages', async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT * FROM message_packages WHERE is_active = true'
    )
    res.json(rows)
  } catch (err) {
    res.status(500).json({ error: '服务器错误' })
  }
})

// 创建支付订单
router.post('/orders', authenticate, async (req, res) => {
  try {
    const { packageId } = req.body
    const userId = req.user.userId

    // 获取套餐信息
    const [packages] = await pool.execute(
      'SELECT * FROM message_packages WHERE id = ?',
      [packageId]
    )

    if (packages.length === 0) {
      return res.status(400).json({ error: '无效的留言包' })
    }

    const package = packages[0]
    const transactionId = `TRX${Date.now()}${Math.floor(Math.random() * 1000)}`

    // 创建订单
    const [result] = await pool.execute(
      `INSERT INTO payment_orders
      (user_id, transaction_id, amount, package_type)
      VALUES (?, ?, ?, ?)`,
      [userId, transactionId, package.price, package.name]
    )

    // 调用微信支付
    const payParams = {
      body: `购买留言包: ${package.name}`,
      out_trade_no: transactionId,
      total_fee: Math.round(package.price * 100), // 转换为分
      spbill_create_ip: req.ip,
      notify_url: `${process.env.BASE_URL}/api/payment/callback`,
      trade_type: 'JSAPI',
      openid: req.user.openid // 需要前端获取用户openid
    }

    const paymentResult = await wechatPay.createOrder(payParams)

    res.status(201).json({
      orderId: result.insertId,
      transactionId,
      amount: package.price,
      packageName: package.name,
      paymentParams: {
        appId: paymentResult.appid,
        timeStamp: Math.floor(Date.now() / 1000).toString(),
        nonceStr: paymentResult.nonce_str,
        package: `prepay_id=${paymentResult.prepay_id}`,
        signType: 'MD5',
        paySign: paymentResult.sign
      }
    })
  } catch (err) {
    console.error('支付订单创建失败:', err)
    res.status(500).json({ error: '支付订单创建失败' })
  }
})

// 支付回调
router.post('/callback', async (req, res) => {
  try {
    // 验证签名
    if (!wechatPay.verifyPayment(req.body)) {
      return res.status(400).json({ error: '签名验证失败' })
    }

    const { out_trade_no, result_code } = req.body

    // 验证订单
    const [orders] = await pool.execute(
      'SELECT * FROM payment_orders WHERE transaction_id = ?',
      [out_trade_no]
    )

    if (orders.length === 0) {
      return res.status(400).json({ error: '无效的订单' })
    }

    const order = orders[0]
    const status = result_code === 'SUCCESS' ? 'completed' : 'failed'

    // 更新订单状态
    await pool.execute(
      'UPDATE payment_orders SET status = ? WHERE transaction_id = ?',
      [status, out_trade_no]
    )

    // 支付成功时增加用户积分
    if (status === 'completed') {
      const [packages] = await pool.execute(
        'SELECT message_count FROM message_packages WHERE name = ?',
        [order.package_type]
      )

      if (packages.length > 0) {
        const messageCount = packages[0].message_count

        // 更新用户积分
        await pool.execute(
          `INSERT INTO user_message_credits (user_id, credits)
          VALUES (?, ?)
          ON DUPLICATE KEY UPDATE credits = credits + ?`,
          [order.user_id, messageCount, messageCount]
        )
      }
    }

    res.json({ return_code: 'SUCCESS', return_msg: 'OK' })
  } catch (err) {
    console.error('支付回调处理失败:', err)
    res.status(500).json({ return_code: 'FAIL', return_msg: '处理失败' })
  }
})

module.exports = router
