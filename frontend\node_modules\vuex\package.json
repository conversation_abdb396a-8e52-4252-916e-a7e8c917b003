{"name": "vuex", "version": "4.0.2", "description": "state management for Vue.js", "main": "dist/vuex.cjs.js", "exports": {".": {"module": "./dist/vuex.esm-bundler.js", "require": "./dist/vuex.cjs.js", "import": "./dist/vuex.mjs"}, "./": "./"}, "module": "dist/vuex.esm-bundler.js", "browser": "dist/vuex.esm-browser.js", "unpkg": "dist/vuex.global.js", "jsdelivr": "dist/vuex.global.js", "typings": "types/index.d.ts", "sideEffects": false, "files": ["dist", "types/index.d.ts", "types/helpers.d.ts", "types/logger.d.ts", "types/vue.d.ts"], "scripts": {"dev": "node examples/server.js", "build": "node scripts/build.js", "lint": "eslint src test", "test": "npm run lint && npm run build && npm run test:types && npm run test:unit && npm run test:ssr && npm run test:e2e && npm run test:esm", "test:unit": "jest --testPathIgnorePatterns test/e2e", "test:e2e": "start-server-and-test dev http://localhost:8080 \"jest --testPathIgnorePatterns test/unit\"", "test:ssr": "cross-env VUE_ENV=server jest --testPathIgnorePatterns test/e2e", "test:types": "tsc -p types/test", "test:esm": "node test/esm/esm-test.js", "coverage": "jest --testPathIgnorePatterns test/e2e --coverage", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release": "node scripts/release.js", "docs": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "yarn docs:build && vitepress serve docs"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vuex.git"}, "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/vuex/issues"}, "homepage": "https://github.com/vuejs/vuex#readme", "peerDependencies": {"vue": "^3.0.2"}, "dependencies": {"@vue/devtools-api": "^6.0.0-beta.11"}, "devDependencies": {"@babel/core": "^7.14.3", "@babel/preset-env": "^7.14.2", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-replace": "^2.4.2", "@types/node": "^15.6.0", "@vue/compiler-sfc": "^3.0.11", "babel-jest": "^26.6.3", "babel-loader": "^8.2.2", "brotli": "^1.3.2", "chalk": "^4.1.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.3", "css-loader": "^2.1.0", "enquirer": "^2.3.5", "eslint": "^7.27.0", "eslint-plugin-vue-libs": "^4.0.0", "execa": "^5.0.0", "express": "^4.17.1", "fs-extra": "^10.0.0", "jest": "^26.6.3", "puppeteer": "^9.1.1", "regenerator-runtime": "^0.13.5", "rollup": "^2.49.0", "rollup-plugin-terser": "^7.0.2", "semver": "^7.3.5", "start-server-and-test": "^1.12.3", "todomvc-app-css": "^2.4.1", "typescript": "^4.2.4", "vitepress": "^0.11.5", "vue": "^3.0.11", "vue-loader": "^16.2.0", "vue-style-loader": "^4.1.3", "webpack": "^4.43.0", "webpack-dev-middleware": "^3.7.2", "webpack-hot-middleware": "^2.25.0"}}