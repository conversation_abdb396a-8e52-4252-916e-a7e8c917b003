<template>
  <div class="min-h-screen bg-gray-100">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <router-view />
    </div>

    <!-- 底部导航栏 -->
    <BottomNavigation />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'
import BottomNavigation from './components/BottomNavigation.vue'

export default defineComponent({
  name: 'App',
  components: {
    BottomNavigation
  },
  created() {
    // 初始化逻辑
  }
})
</script>

<style>
/* 全局样式 */
.main-content {
  padding-bottom: 70px; /* 为底部导航栏留出空间 */
  min-height: calc(100vh - 70px);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .main-content {
    padding: 0 16px 70px 16px;
  }
}
</style>
