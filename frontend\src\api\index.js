import axios from 'axios'

const API_URL = 'http://localhost:3000/api'

export default {
  // 认证相关
  auth: {
    login(credentials) {
      return axios.post(`${API_URL}/auth/login`, credentials)
    },
    register(credentials) {
      return axios.post(`${API_URL}/auth/register`, credentials)
    }
  },
  
  // 留言相关
  messages: {
    create(message) {
      return axios.post(`${API_URL}/messages`, message)
    },
    getPublic() {
      return axios.get(`${API_URL}/messages/public`)
    },
    search(name, code) {
      return axios.get(`${API_URL}/messages/search`, {
        params: { name, code }
      })
    }
  }
}
