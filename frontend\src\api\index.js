import axios from 'axios'

const API_URL = 'http://localhost:3000/api'

// 设置请求拦截器，自动添加token
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

export default {
  // 认证相关
  auth: {
    login(credentials) {
      return axios.post(`${API_URL}/auth/login`, credentials)
    },
    register(credentials) {
      return axios.post(`${API_URL}/auth/register`, credentials)
    },
    getCredits() {
      return axios.get(`${API_URL}/auth/credits`)
    }
  },

  // 留言相关
  messages: {
    create(message) {
      return axios.post(`${API_URL}/messages`, message)
    },
    getPublic() {
      return axios.get(`${API_URL}/messages/public`)
    },
    search(name, code) {
      return axios.get(`${API_URL}/messages/search`, {
        params: { name, code }
      })
    },
    getMy() {
      return axios.get(`${API_URL}/messages/my`)
    },
    like(messageId) {
      return axios.post(`${API_URL}/messages/${messageId}/like`)
    }
  },

  // 支付相关
  payment: {
    getPackages() {
      return axios.get(`${API_URL}/payment/packages`)
    },
    createOrder(packageId) {
      return axios.post(`${API_URL}/payment/orders`, { packageId })
    }
  },

  // 管理员相关
  admin: {
    getMessages() {
      return axios.get(`${API_URL}/admin/messages`)
    },
    approveMessage(messageId) {
      return axios.post(`${API_URL}/admin/messages/${messageId}/approve`)
    },
    rejectMessage(messageId) {
      return axios.post(`${API_URL}/admin/messages/${messageId}/reject`)
    }
  },

  // 通用请求方法
  get(url) {
    return axios.get(`${API_URL}${url}`)
  },
  post(url, data) {
    return axios.post(`${API_URL}${url}`, data)
  },
  put(url, data) {
    return axios.put(`${API_URL}${url}`, data)
  },
  delete(url) {
    return axios.delete(`${API_URL}${url}`)
  }
}
