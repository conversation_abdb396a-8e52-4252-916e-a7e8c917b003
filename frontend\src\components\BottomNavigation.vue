<template>
  <div class="bottom-nav">
    <div class="nav-item" :class="{ active: isActive('/') }" @click="navigateTo('/')">
      <div class="nav-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
        </svg>
      </div>
      <span class="nav-text">首页</span>
    </div>

    <div class="nav-item" :class="{ active: isActive('/search') }" @click="navigateTo('/search')">
      <div class="nav-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      </div>
      <span class="nav-text">找留言</span>
    </div>

    <div class="nav-item" :class="{ active: isActive('/create') }" @click="navigateTo('/create')">
      <div class="nav-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
        </svg>
      </div>
      <span class="nav-text">写留言</span>
    </div>

    <div class="nav-item" :class="{ active: isActive('/public') }" @click="navigateTo('/public')">
      <div class="nav-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
        </svg>
      </div>
      <span class="nav-text">公开留言</span>
    </div>

    <div class="nav-item" :class="{ active: isActive(isLoggedIn ? '/profile' : '/login') }" @click="navigateTo(isLoggedIn ? '/profile' : '/login')">
      <div class="nav-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      </div>
      <span class="nav-text">{{ isLoggedIn ? '我的' : '登录' }}</span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'BottomNavigation',
  setup() {
    const router = useRouter()
    const store = useStore()

    const isLoggedIn = computed(() => store.getters.isLoggedIn)
    const currentRoute = computed(() => router.currentRoute.value.path)

    const navigateTo = (path) => {
      if (currentRoute.value !== path) {
        router.push(path)
      }
    }

    const isActive = (path) => {
      return currentRoute.value === path
    }

    return {
      isLoggedIn,
      navigateTo,
      isActive
    }
  }
}
</script>

<style scoped>
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-around;
  padding: 8px 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px 8px;
  cursor: pointer;
  transition: color 0.2s;
  min-width: 60px;
}

.nav-item:hover {
  color: #3b82f6;
}

.nav-item.active {
  color: #3b82f6;
  font-weight: 600;
}

.nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
}

.nav-icon svg {
  width: 100%;
  height: 100%;
}

.nav-text {
  font-size: 12px;
  font-weight: 500;
}

.nav-item.active .nav-text {
  font-weight: 600;
}

/* 为底部导航栏留出空间 */
body {
  padding-bottom: 70px;
}
</style>
