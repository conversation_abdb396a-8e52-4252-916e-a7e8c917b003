import { createStore } from 'vuex'
import axios from 'axios'

const API_URL = 'http://localhost:3000/api'

export default createStore({
  state: {
    user: null,
    token: null,
    credits: 0
  },
  mutations: {
    setUser(state, { user, token }) {
      state.user = user
      state.token = token
      state.credits = user?.credits || 0
      if (token) {
        localStorage.setItem('token', token)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      }
    },
    setCredits(state, credits) {
      state.credits = credits
    },
    logout(state) {
      state.user = null
      state.token = null
      state.credits = 0
      localStorage.removeItem('token')
      delete axios.defaults.headers.common['Authorization']
    }
  },
  actions: {
    async login({ commit }, credentials) {
      try {
        const response = await axios.post(`${API_URL}/auth/login`, credentials)
        commit('setUser', {
          user: { username: response.data.username },
          token: response.data.token
        })
        return response.data
      } catch (error) {
        throw error.response?.data?.error || '登录失败'
      }
    },
    async register({ commit }, credentials) {
      try {
        const response = await axios.post(`${API_URL}/auth/register`, credentials)
        return response.data
      } catch (error) {
        throw error.response?.data?.error || '注册失败'
      }
    },
    initialize({ commit }) {
      const token = localStorage.getItem('token')
      if (token) {
        commit('setUser', { user: null, token })
      }
    }
  },
  getters: {
    isAuthenticated: state => !!state.token,
    isLoggedIn: state => !!state.token,
    currentUser: state => state.user,
    userCredits: state => state.credits
  }
})
