<template>
  <div class="admin-container">
    <h1>管理后台</h1>
    
    <div class="admin-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        :class="{ active: activeTab === tab.id }"
        @click="activeTab = tab.id"
      >
        {{ tab.name }}
      </button>
    </div>

    <div class="tab-content">
      <!-- 待审核留言 -->
      <div v-if="activeTab === 'pendingMessages'" class="pending-messages">
        <h2>待审核留言</h2>
        <div v-if="pendingMessages.length === 0" class="empty-state">
          暂无待审核留言
        </div>
        <div v-else class="message-list">
          <div v-for="message in pendingMessages" :key="message.id" class="message-item">
            <div class="message-content">{{ message.content }}</div>
            <div class="message-meta">
              <span>来自: {{ message.sender_name }}</span>
              <span>时间: {{ formatDate(message.created_at) }}</span>
            </div>
            <div class="message-actions">
              <button @click="approveMessage(message.id)" class="approve-btn">通过</button>
              <button @click="rejectMessage(message.id)" class="reject-btn">拒绝</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 敏感词管理 -->
      <div v-if="activeTab === 'sensitiveWords'" class="sensitive-words">
        <h2>敏感词管理</h2>
        <div class="add-word-form">
          <input v-model="newWord" placeholder="输入敏感词" />
          <input v-model="newCategory" placeholder="分类(可选)" />
          <button @click="addSensitiveWord" class="add-btn">添加</button>
        </div>
        <div class="word-list">
          <div v-for="word in sensitiveWords" :key="word.id" class="word-item">
            <span class="word-text">{{ word.word }}</span>
            <span class="word-category">{{ word.category || '无分类' }}</span>
            <button @click="deleteWord(word.id)" class="delete-btn">删除</button>
          </div>
        </div>
      </div>

      <!-- 支付订单 -->
      <div v-if="activeTab === 'paymentOrders'" class="payment-orders">
        <h2>支付订单</h2>
        <div class="order-list">
          <div v-for="order in paymentOrders" :key="order.id" class="order-item">
            <div class="order-info">
              <span>订单号: {{ order.transaction_id }}</span>
              <span>用户ID: {{ order.user_id }}</span>
              <span>金额: ¥{{ order.amount }}</span>
              <span>套餐: {{ order.package_type }}</span>
              <span>状态: {{ order.status }}</span>
              <span>时间: {{ formatDate(order.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div v-if="activeTab === 'operationLogs'" class="operation-logs">
        <h2>操作日志</h2>
        <div class="log-controls">
          <button @click="prevLogPage" :disabled="logPage <= 1">上一页</button>
          <span>第 {{ logPage }} 页 / 共 {{ Math.ceil(logTotal / logLimit) }} 页</span>
          <button @click="nextLogPage" :disabled="logPage >= Math.ceil(logTotal / logLimit)">下一页</button>
          <select v-model="logLimit" @change="fetchLogs">
            <option value="10">10条/页</option>
            <option value="20">20条/页</option>
            <option value="50">50条/页</option>
          </select>
        </div>
        <div class="log-list">
          <div v-for="log in operationLogs" :key="log.id" class="log-item">
            <div class="log-action">{{ log.action }}</div>
            <div class="log-meta">
              <span>用户: {{ log.username || '匿名' }}</span>
              <span>IP: {{ log.ip }}</span>
              <span>时间: {{ formatDate(log.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import api from '@/api'

export default {
  name: 'AdminView',
  setup() {
    const router = useRouter()
    const activeTab = ref('pendingMessages')
    const tabs = [
      { id: 'pendingMessages', name: '留言审核' },
      { id: 'sensitiveWords', name: '敏感词' },
      { id: 'paymentOrders', name: '支付订单' },
      { id: 'operationLogs', name: '操作日志' }
    ]

    const pendingMessages = ref([])
    const sensitiveWords = ref([])
    const paymentOrders = ref([])
    const operationLogs = ref([])
    const newWord = ref('')
    const newCategory = ref('')
    const logPage = ref(1)
    const logLimit = ref(20)
    const logTotal = ref(0)

    const fetchPendingMessages = async () => {
      try {
        const { data } = await api.get('/admin/messages/pending')
        pendingMessages.value = data
      } catch (error) {
        console.error('获取待审核留言失败:', error)
      }
    }

    const fetchSensitiveWords = async () => {
      try {
        const { data } = await api.get('/admin/sensitive-words')
        sensitiveWords.value = data
      } catch (error) {
        console.error('获取敏感词列表失败:', error)
      }
    }

    const fetchPaymentOrders = async () => {
      try {
        const { data } = await api.get('/admin/payment-orders')
        paymentOrders.value = data
      } catch (error) {
        console.error('获取支付订单失败:', error)
      }
    }

    const fetchLogs = async () => {
      try {
        const { data } = await api.get('/admin/logs', {
          params: {
            page: logPage.value,
            limit: logLimit.value
          }
        })
        operationLogs.value = data.logs
        logTotal.value = data.total
      } catch (error) {
        console.error('获取操作日志失败:', error)
      }
    }

    const prevLogPage = () => {
      if (logPage.value > 1) {
        logPage.value--
        fetchLogs()
      }
    }

    const nextLogPage = () => {
      if (logPage.value < Math.ceil(logTotal.value / logLimit.value)) {
        logPage.value++
        fetchLogs()
      }
    }

    const approveMessage = async (id) => {
      try {
        await api.put(`/admin/messages/${id}/approve`)
        await fetchPendingMessages()
      } catch (error) {
        console.error('审核留言失败:', error)
      }
    }

    const rejectMessage = async (id) => {
      try {
        await api.delete(`/messages/${id}`)
        await fetchPendingMessages()
      } catch (error) {
        console.error('拒绝留言失败:', error)
      }
    }

    const addSensitiveWord = async () => {
      if (!newWord.value.trim()) return
      
      try {
        await api.post('/admin/sensitive-words', {
          word: newWord.value,
          category: newCategory.value
        })
        newWord.value = ''
        newCategory.value = ''
        await fetchSensitiveWords()
      } catch (error) {
        console.error('添加敏感词失败:', error)
      }
    }

    const deleteWord = async (id) => {
      try {
        await api.delete(`/admin/sensitive-words/${id}`)
        await fetchSensitiveWords()
      } catch (error) {
        console.error('删除敏感词失败:', error)
      }
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString()
    }

    onMounted(async () => {
      await fetchPendingMessages()
      await fetchSensitiveWords()
      await fetchPaymentOrders()
      await fetchLogs()
    })

    return {
      activeTab,
      tabs,
      pendingMessages,
      sensitiveWords,
      paymentOrders,
      operationLogs,
      newWord,
      newCategory,
      logPage,
      logLimit,
      logTotal,
      approveMessage,
      rejectMessage,
      addSensitiveWord,
      deleteWord,
      fetchLogs,
      prevLogPage,
      nextLogPage,
      formatDate
    }
  }
}
</script>

<style scoped>
.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.admin-tabs {
  display: flex;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.admin-tabs button {
  padding: 10px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  border-bottom: 3px solid transparent;
}

.admin-tabs button.active {
  border-bottom: 3px solid #42b983;
  font-weight: bold;
}

.tab-content {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #888;
}

.message-list, .word-list, .order-list, .log-list {
  display: grid;
  gap: 15px;
}

.message-item, .word-item, .order-item, .log-item {
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 4px;
}

.message-content {
  margin-bottom: 10px;
}

.message-meta, .log-meta {
  display: flex;
  justify-content: space-between;
  color: #666;
  font-size: 14px;
}

.message-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.approve-btn {
  background: #42b983;
  color: white;
}

.reject-btn {
  background: #ff4757;
  color: white;
}

.add-word-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.add-word-form input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.word-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.word-text {
  font-weight: bold;
}

.word-category {
  color: #666;
  font-size: 14px;
}

.delete-btn {
  background: #ff4757;
  color: white;
}

.order-info {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

.log-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 15px;
}

.log-controls button {
  padding: 5px 10px;
}

.log-controls select {
  padding: 5px;
}

.log-action {
  font-weight: bold;
  margin-bottom: 5px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  opacity: 0.9;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
