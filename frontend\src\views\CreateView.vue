<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 p-4">
    <div class="max-w-md mx-auto">
      <!-- 头部 -->
      <div class="text-center mb-8">
        <div class="text-6xl mb-4">✍️</div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">写留言</h1>
        <p class="text-gray-600">给TA写一条温暖的留言</p>
      </div>

      <!-- 表单 -->
      <div class="bg-white rounded-2xl shadow-xl p-6">
        <form @submit.prevent="submitMessage" class="space-y-6">
          <div>
            <label class="block mb-2 text-sm font-medium text-gray-700">
              收件人名字 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.receiverName"
              type="text"
              class="input-field"
              placeholder="请输入收件人的名字"
              required
            >
          </div>

          <div>
            <label class="block mb-2 text-sm font-medium text-gray-700">
              暗号 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.code"
              type="text"
              class="input-field"
              placeholder="请输入暗号（收件人需要此暗号才能查看）"
              required
            >
            <p class="text-xs text-gray-500 mt-1">暗号是收件人查看留言时需要输入的密码</p>
          </div>

          <div>
            <label class="block mb-2 text-sm font-medium text-gray-700">
              留言内容 <span class="text-red-500">*</span>
            </label>
            <textarea
              v-model="form.content"
              class="input-field"
              rows="6"
              placeholder="在这里写下你想说的话..."
              required
            ></textarea>
            <div class="text-right text-xs text-gray-500 mt-1">
              {{ form.content.length }}/500
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <input
              v-model="form.isPublic"
              type="checkbox"
              id="isPublic"
              class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            >
            <label for="isPublic" class="text-sm text-gray-700">
              设为公开留言（其他人也可以看到）
            </label>
          </div>

          <!-- 积分提示 -->
          <div v-if="userCredits !== null" class="bg-blue-50 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700">当前积分：</span>
              <span class="font-bold text-blue-600">{{ userCredits }}</span>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              发送留言需要消耗 1 积分
            </div>
          </div>

          <button
            type="submit"
            class="btn w-full"
            :disabled="loading || userCredits < 1"
          >
            {{ loading ? '发送中...' : '发送留言' }}
          </button>

          <div v-if="userCredits < 1" class="text-center">
            <router-link to="/profile" class="text-blue-500 hover:text-blue-700 text-sm">
              积分不足？点击购买留言包 →
            </router-link>
          </div>
        </form>

        <!-- 错误提示 -->
        <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          {{ error }}
        </div>

        <!-- 成功提示 -->
        <div v-if="success" class="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg">
          {{ success }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import api from '@/api'

export default {
  name: 'CreateView',
  setup() {
    const router = useRouter()
    const store = useStore()

    const form = ref({
      receiverName: '',
      code: '',
      content: '',
      isPublic: false
    })

    const loading = ref(false)
    const error = ref('')
    const success = ref('')

    const userCredits = computed(() => store.getters.userCredits)
    const isLoggedIn = computed(() => store.getters.isLoggedIn)

    const submitMessage = async () => {
      error.value = ''
      success.value = ''

      // 检查登录状态
      if (!isLoggedIn.value) {
        router.push('/login')
        return
      }

      // 验证表单
      if (!form.value.receiverName || !form.value.code || !form.value.content) {
        error.value = '请填写所有必填字段'
        return
      }

      if (form.value.content.length > 500) {
        error.value = '留言内容不能超过500字'
        return
      }

      if (userCredits.value < 1) {
        error.value = '积分不足，请先购买留言包'
        return
      }

      loading.value = true

      try {
        const response = await api.messages.create({
          receiverName: form.value.receiverName,
          code: form.value.code,
          content: form.value.content,
          isPublic: form.value.isPublic
        })

        success.value = '留言发送成功！'

        // 更新用户积分
        store.commit('setCredits', response.data.remainingCredits)

        // 重置表单
        form.value = {
          receiverName: '',
          code: '',
          content: '',
          isPublic: false
        }

        // 3秒后跳转到首页
        setTimeout(() => {
          router.push('/')
        }, 3000)

      } catch (err) {
        error.value = err.response?.data?.error || '发送失败，请重试'
      } finally {
        loading.value = false
      }
    }

    // 获取用户积分
    const fetchUserCredits = async () => {
      if (isLoggedIn.value) {
        try {
          const response = await api.auth.getCredits()
          store.commit('setCredits', response.data.credits)
        } catch (error) {
          console.error('获取积分失败:', error)
        }
      }
    }

    onMounted(() => {
      fetchUserCredits()
    })

    return {
      form,
      loading,
      error,
      success,
      userCredits,
      submitMessage
    }
  }
}
</script>

<style scoped>
.input-field {
  @apply border border-gray-300 rounded-lg p-3 w-full focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all resize-none;
}

.btn {
  @apply py-3 px-4 rounded-lg text-white bg-green-500 hover:bg-green-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
