<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50">
    <!-- 头部区域 -->
    <div class="text-center py-8">
      <h1 class="text-4xl font-bold text-gray-800 mb-2">💌 留言墙</h1>
      <p class="text-gray-600">分享你的心声，传递温暖</p>
    </div>

    <!-- 主要功能按钮 -->
    <div class="px-6 mb-8">
      <div class="flex flex-col space-y-4 max-w-sm mx-auto">
        <router-link to="/search" class="action-btn search-btn">
          <div class="btn-icon">🔍</div>
          <div class="btn-content">
            <div class="btn-title">找留言</div>
            <div class="btn-subtitle">输入名字和暗号查找</div>
          </div>
        </router-link>

        <router-link to="/create" class="action-btn create-btn">
          <div class="btn-icon">✍️</div>
          <div class="btn-content">
            <div class="btn-title">写留言</div>
            <div class="btn-subtitle">给TA写一条温暖的留言</div>
          </div>
        </router-link>

        <router-link to="/public" class="action-btn public-btn">
          <div class="btn-icon">🌟</div>
          <div class="btn-content">
            <div class="btn-title">公开留言</div>
            <div class="btn-subtitle">查看所有公开的留言</div>
          </div>
        </router-link>
      </div>
    </div>

    <!-- 最新公开留言 -->
    <div class="px-4">
      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-gray-800">最新留言</h2>
          <router-link to="/public" class="text-blue-500 text-sm hover:text-blue-700">
            查看更多 →
          </router-link>
        </div>

        <div v-if="messages.length" class="space-y-3">
          <div
            v-for="message in messages.slice(0, 5)"
            :key="message.id"
            class="message-card"
          >
            <div class="flex items-start space-x-3">
              <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white font-bold text-sm">
                {{ getInitial(message.sender_name) }}
              </div>
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-1">
                  <span class="font-medium text-gray-800">{{ message.sender_name }}</span>
                  <span class="text-xs text-gray-500">{{ formatDate(message.created_at) }}</span>
                </div>
                <p class="text-gray-700 text-sm line-clamp-2">{{ message.content }}</p>
                <div class="flex items-center space-x-4 mt-2">
                  <button class="flex items-center space-x-1 text-gray-500 hover:text-red-500">
                    <span class="text-xs">❤️</span>
                    <span class="text-xs">{{ message.likes || 0 }}</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center py-8 text-gray-500">
          <div class="text-4xl mb-2">📝</div>
          <p>还没有公开留言</p>
          <p class="text-sm">快来写第一条留言吧！</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue'
import api from '@/api'

export default defineComponent({
  name: 'HomeView',
  setup() {
    const messages = ref([])

    const fetchPublicMessages = async () => {
      try {
        const response = await api.messages.getPublic()
        messages.value = response.data
      } catch (error) {
        console.error('获取公开留言失败:', error)
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

      return date.toLocaleDateString('zh-CN')
    }

    const getInitial = (name) => {
      return name ? name.charAt(0).toUpperCase() : 'U'
    }

    onMounted(() => {
      fetchPublicMessages()
    })

    return {
      messages,
      formatDate,
      getInitial
    }
  }
})
</script>

<style scoped>
.action-btn {
  @apply flex items-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-200 text-left;
}

.action-btn:hover {
  transform: translateY(-2px);
}

.btn-icon {
  @apply text-2xl mr-4 flex-shrink-0;
}

.btn-content {
  @apply flex-1;
}

.btn-title {
  @apply font-semibold text-gray-800 text-lg;
}

.btn-subtitle {
  @apply text-gray-600 text-sm mt-1;
}

.search-btn:hover {
  @apply bg-blue-50 border-blue-200;
}

.create-btn:hover {
  @apply bg-green-50 border-green-200;
}

.public-btn:hover {
  @apply bg-purple-50 border-purple-200;
}

.message-card {
  @apply p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
