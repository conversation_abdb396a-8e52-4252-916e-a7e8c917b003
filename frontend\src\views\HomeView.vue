<template>
  <div class="container mx-auto p-4">
    <h1 class="text-3xl font-bold mb-8">留言墙</h1>
    <div class="flex flex-col space-y-4">
      <router-link to="/search" class="btn btn-primary">找留言</router-link>
      <router-link to="/create" class="btn btn-primary">写留言</router-link>
      <router-link to="/public" class="btn btn-primary">公开留言</router-link>
    </div>

    <!-- 留言列表 -->
    <div class="mt-8 space-y-4" v-if="messages.length">
      <div v-for="message in messages" :key="message.id" class="p-4 border rounded">
        <h3 class="font-bold">{{ message.name }}</h3>
        <p class="text-gray-600">{{ message.content }}</p>
        <p class="text-sm text-gray-400">{{ formatDate(message.createdAt) }}</p>
      </div>
    </div>
    <div v-else class="mt-8 text-gray-500">
      暂无公开留言
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue'
import api from '@/api'

export default defineComponent({
  name: 'HomeView',
  setup() {
    const messages = ref([])

    const fetchPublicMessages = async () => {
      try {
        const response = await api.messages.getPublic()
        messages.value = response.data
      } catch (error) {
        console.error('获取公开留言失败:', error)
      }
    }

    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString()
    }

    onMounted(() => {
      fetchPublicMessages()
    })

    return {
      messages,
      formatDate
    }
  }
})
</script>

<style scoped>
.btn {
  @apply py-2 px-4 rounded text-white bg-blue-500 hover:bg-blue-700 transition-colors;
}
</style>
