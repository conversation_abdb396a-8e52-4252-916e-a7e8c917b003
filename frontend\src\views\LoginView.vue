<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md">
      <div class="text-center mb-8">
        <div class="text-6xl mb-4">💌</div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">欢迎回来</h1>
        <p class="text-gray-600">登录到你的留言墙账户</p>
      </div>

      <form @submit.prevent="login" class="space-y-6">
        <div>
          <label class="block mb-2 text-sm font-medium text-gray-700">用户名</label>
          <input
            v-model="form.username"
            type="text"
            class="input-field"
            placeholder="请输入用户名"
            required
          >
        </div>

        <div>
          <label class="block mb-2 text-sm font-medium text-gray-700">密码</label>
          <input
            v-model="form.password"
            type="password"
            class="input-field"
            placeholder="请输入密码"
            required
          >
        </div>

        <button
          type="submit"
          class="btn w-full"
          :disabled="loading"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>

        <div class="text-center">
          <router-link to="/register" class="text-blue-500 hover:text-blue-700 text-sm">
            没有账号？立即注册
          </router-link>
        </div>
      </form>

      <!-- 错误提示 -->
      <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
        {{ error }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'LoginView',
  setup() {
    const router = useRouter()
    const store = useStore()

    const form = ref({
      username: '',
      password: ''
    })

    const loading = ref(false)
    const error = ref('')

    const login = async () => {
      error.value = ''

      if (!form.value.username || !form.value.password) {
        error.value = '请填写用户名和密码'
        return
      }

      loading.value = true

      try {
        await store.dispatch('login', {
          username: form.value.username,
          password: form.value.password
        })

        router.push('/')
      } catch (err) {
        error.value = err || '登录失败，请重试'
      } finally {
        loading.value = false
      }
    }

    return {
      form,
      loading,
      error,
      login
    }
  }
}
</script>

<style scoped>
.input-field {
  @apply border border-gray-300 rounded-lg p-3 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all;
}

.btn {
  @apply py-3 px-4 rounded-lg text-white bg-blue-500 hover:bg-blue-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
