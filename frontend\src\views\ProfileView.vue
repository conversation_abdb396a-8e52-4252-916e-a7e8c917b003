<template>
  <div class="container mx-auto p-4">
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="flex items-center mb-4">
        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold">
          {{ userInitial }}
        </div>
        <div class="ml-4">
          <h2 class="text-xl font-bold">{{ currentUser?.username || '用户' }}</h2>
          <p class="text-gray-600">留言积分：{{ userCredits }}</p>
        </div>
      </div>
      
      <div class="grid grid-cols-2 gap-4 mb-4">
        <div class="bg-blue-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-blue-600">{{ userCredits }}</div>
          <div class="text-sm text-gray-600">剩余积分</div>
        </div>
        <div class="bg-green-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-green-600">{{ myMessagesCount }}</div>
          <div class="text-sm text-gray-600">我的留言</div>
        </div>
      </div>
      
      <button @click="showPurchaseModal = true" class="btn btn-primary w-full mb-4">
        购买留言包
      </button>
      
      <button @click="logout" class="btn btn-secondary w-full">
        退出登录
      </button>
    </div>
    
    <!-- 我的留言列表 -->
    <div class="bg-white rounded-lg shadow-md p-6">
      <h3 class="text-lg font-bold mb-4">我的留言</h3>
      
      <div v-if="myMessages.length === 0" class="text-center text-gray-500 py-8">
        暂无留言
      </div>
      
      <div v-else class="space-y-4">
        <div 
          v-for="message in myMessages" 
          :key="message.id"
          class="border rounded-lg p-4"
        >
          <div class="flex justify-between items-start mb-2">
            <h4 class="font-medium">给 {{ message.receiver_name }}</h4>
            <span class="text-xs text-gray-500">{{ formatDate(message.created_at) }}</span>
          </div>
          <p class="text-gray-700 mb-2">{{ message.content }}</p>
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-500">暗号：{{ message.code }}</span>
            <div class="flex items-center space-x-2">
              <span :class="message.is_public ? 'text-green-600' : 'text-gray-600'">
                {{ message.is_public ? '公开' : '私密' }}
              </span>
              <span :class="message.is_approved ? 'text-green-600' : 'text-yellow-600'">
                {{ message.is_approved ? '已审核' : '待审核' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div v-if="myMessages.length > 0" class="mt-4 text-center">
        <button @click="loadMoreMessages" class="text-blue-500 hover:text-blue-700">
          查看更多
        </button>
      </div>
    </div>
    
    <!-- 购买留言包模态框 -->
    <div v-if="showPurchaseModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-bold mb-4">选择留言包</h3>
        
        <div class="space-y-3 mb-6">
          <div 
            v-for="pkg in packages" 
            :key="pkg.id"
            class="border rounded-lg p-4 cursor-pointer hover:border-blue-500"
            :class="{ 'border-blue-500 bg-blue-50': selectedPackage?.id === pkg.id }"
            @click="selectedPackage = pkg"
          >
            <div class="flex justify-between items-center">
              <div>
                <div class="font-medium">{{ pkg.name }}</div>
                <div class="text-sm text-gray-600">{{ pkg.message_count }}条留言</div>
              </div>
              <div class="text-lg font-bold text-blue-600">¥{{ pkg.price }}</div>
            </div>
          </div>
        </div>
        
        <div class="flex space-x-3">
          <button @click="showPurchaseModal = false" class="btn btn-secondary flex-1">
            取消
          </button>
          <button @click="purchasePackage" class="btn btn-primary flex-1" :disabled="!selectedPackage">
            确认购买
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import api from '@/api'

export default {
  name: 'ProfileView',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const myMessages = ref([])
    const myMessagesCount = ref(0)
    const showPurchaseModal = ref(false)
    const selectedPackage = ref(null)
    const packages = ref([])
    
    const currentUser = computed(() => store.getters.currentUser)
    const userCredits = computed(() => store.getters.userCredits)
    const userInitial = computed(() => {
      return currentUser.value?.username?.charAt(0).toUpperCase() || 'U'
    })
    
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleString('zh-CN')
    }
    
    const loadMyMessages = async () => {
      try {
        const response = await api.get('/messages/my')
        myMessages.value = response.data
        myMessagesCount.value = response.data.length
      } catch (error) {
        console.error('获取我的留言失败:', error)
      }
    }
    
    const loadPackages = async () => {
      try {
        const response = await api.get('/payment/packages')
        packages.value = response.data
      } catch (error) {
        console.error('获取留言包失败:', error)
      }
    }
    
    const purchasePackage = async () => {
      if (!selectedPackage.value) return
      
      try {
        // 这里应该调用支付接口
        console.log('购买留言包:', selectedPackage.value)
        showPurchaseModal.value = false
        selectedPackage.value = null
      } catch (error) {
        console.error('购买失败:', error)
      }
    }
    
    const loadMoreMessages = () => {
      // 实现分页加载
      console.log('加载更多留言')
    }
    
    const logout = () => {
      store.dispatch('logout')
      router.push('/login')
    }
    
    onMounted(() => {
      loadMyMessages()
      loadPackages()
    })
    
    return {
      currentUser,
      userCredits,
      userInitial,
      myMessages,
      myMessagesCount,
      showPurchaseModal,
      selectedPackage,
      packages,
      formatDate,
      loadMoreMessages,
      purchasePackage,
      logout
    }
  }
}
</script>

<style scoped>
.btn {
  @apply py-2 px-4 rounded-lg font-medium transition-colors;
}

.btn-primary {
  @apply bg-blue-500 text-white hover:bg-blue-600;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>
