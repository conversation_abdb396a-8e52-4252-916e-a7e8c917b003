<template>
  <div class="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 p-4">
    <div class="max-w-md mx-auto">
      <!-- 头部 -->
      <div class="text-center mb-8">
        <div class="text-6xl mb-4">🌟</div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">公开留言</h1>
        <p class="text-gray-600">分享美好，传递温暖</p>
      </div>

      <!-- 统计信息 -->
      <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
        <div class="flex justify-between items-center">
          <div class="text-center">
            <div class="text-2xl font-bold text-orange-600">{{ messages.length }}</div>
            <div class="text-xs text-gray-500">条留言</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-red-600">{{ totalLikes }}</div>
            <div class="text-xs text-gray-500">个赞</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ uniqueSenders }}</div>
            <div class="text-xs text-gray-500">位用户</div>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
        <p class="mt-2 text-gray-600">加载中...</p>
      </div>

      <!-- 留言列表 -->
      <div v-else-if="messages.length > 0" class="space-y-4">
        <div
          v-for="message in messages"
          :key="message.id"
          class="bg-white rounded-xl shadow-sm p-6 hover:shadow-md transition-shadow"
        >
          <div class="flex items-start space-x-4 mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full flex items-center justify-center text-white font-bold">
              {{ getInitial(message.sender_name) }}
            </div>
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-1">
                <span class="font-medium text-gray-800">{{ message.sender_name }}</span>
                <span class="text-xs text-gray-500">{{ formatDate(message.created_at) }}</span>
              </div>
              <div class="text-xs text-gray-500">
                给 {{ message.receiver_name }} 的留言
              </div>
            </div>
          </div>

          <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 mb-4">
            <p class="text-gray-800 leading-relaxed">{{ message.content }}</p>
          </div>

          <div class="flex items-center justify-between">
            <button
              @click="likeMessage(message.id)"
              class="flex items-center space-x-2 text-gray-500 hover:text-red-500 transition-colors"
              :disabled="likedMessages.includes(message.id)"
            >
              <span class="text-lg">{{ likedMessages.includes(message.id) ? '❤️' : '🤍' }}</span>
              <span class="text-sm font-medium">{{ message.likes || 0 }}</span>
            </button>
            <div class="flex items-center space-x-2 text-xs text-gray-500">
              <span>{{ message.is_approved ? '✅ 已审核' : '⏳ 待审核' }}</span>
            </div>
          </div>
        </div>

        <!-- 加载更多按钮 -->
        <div v-if="hasMore" class="text-center py-4">
          <button
            @click="loadMore"
            class="btn"
            :disabled="loadingMore"
          >
            {{ loadingMore ? '加载中...' : '加载更多' }}
          </button>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="bg-white rounded-xl shadow-sm p-8 text-center">
        <div class="text-6xl mb-4">📝</div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">还没有公开留言</h3>
        <p class="text-gray-600 mb-4">成为第一个分享留言的人吧！</p>
        <router-link to="/create" class="btn">
          ✍️ 写留言
        </router-link>
      </div>

      <!-- 浮动写留言按钮 -->
      <router-link
        to="/create"
        class="fixed bottom-20 right-4 w-14 h-14 bg-orange-500 hover:bg-orange-600 text-white rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all"
      >
        <span class="text-2xl">✍️</span>
      </router-link>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import api from '@/api'

export default {
  name: 'PublicView',
  setup() {
    const messages = ref([])
    const loading = ref(true)
    const loadingMore = ref(false)
    const hasMore = ref(false)
    const page = ref(1)
    const likedMessages = ref([])

    // 计算属性
    const totalLikes = computed(() => {
      return messages.value.reduce((sum, message) => sum + (message.likes || 0), 0)
    })

    const uniqueSenders = computed(() => {
      const senders = new Set(messages.value.map(m => m.sender_name))
      return senders.size
    })

    // 获取公开留言
    const fetchPublicMessages = async (pageNum = 1) => {
      try {
        const response = await api.messages.getPublic()

        if (pageNum === 1) {
          messages.value = response.data
        } else {
          messages.value.push(...response.data)
        }

        // 这里可以根据实际API返回的分页信息来设置hasMore
        hasMore.value = response.data.length === 10 // 假设每页10条

      } catch (error) {
        console.error('获取公开留言失败:', error)
      } finally {
        loading.value = false
        loadingMore.value = false
      }
    }

    // 点赞功能
    const likeMessage = async (messageId) => {
      if (likedMessages.value.includes(messageId)) {
        return // 已经点过赞了
      }

      try {
        await api.messages.like(messageId)

        // 更新本地数据
        const message = messages.value.find(m => m.id === messageId)
        if (message) {
          message.likes = (message.likes || 0) + 1
          likedMessages.value.push(messageId)
        }

      } catch (error) {
        console.error('点赞失败:', error)
      }
    }

    // 加载更多
    const loadMore = async () => {
      if (loadingMore.value) return

      loadingMore.value = true
      page.value += 1
      await fetchPublicMessages(page.value)
    }

    // 格式化日期
    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

      return date.toLocaleDateString('zh-CN')
    }

    // 获取用户名首字母
    const getInitial = (name) => {
      return name ? name.charAt(0).toUpperCase() : 'U'
    }

    // 组件挂载时获取数据
    onMounted(() => {
      fetchPublicMessages()
    })

    return {
      messages,
      loading,
      loadingMore,
      hasMore,
      likedMessages,
      totalLikes,
      uniqueSenders,
      likeMessage,
      loadMore,
      formatDate,
      getInitial
    }
  }
}
</script>

<style scoped>
.btn {
  @apply py-3 px-6 rounded-lg text-white bg-orange-500 hover:bg-orange-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.space-y-4 > * {
  animation: fadeIn 0.5s ease-out;
}
</style>
