<template>
  <div class="container mx-auto p-4 max-w-md">
    <h1 class="text-3xl font-bold mb-8 text-center">注册</h1>
    
    <form @submit.prevent="register" class="space-y-4">
      <div>
        <label class="block mb-2 text-sm font-medium">用户名</label>
        <input 
          v-model="form.username" 
          type="text"
          class="input-field"
          placeholder="请输入用户名"
          required
        >
      </div>
      
      <div>
        <label class="block mb-2 text-sm font-medium">密码</label>
        <input 
          v-model="form.password" 
          type="password"
          class="input-field"
          placeholder="请输入密码"
          required
        >
      </div>
      
      <div>
        <label class="block mb-2 text-sm font-medium">确认密码</label>
        <input 
          v-model="form.confirmPassword" 
          type="password"
          class="input-field"
          placeholder="请再次输入密码"
          required
        >
      </div>
      
      <button 
        type="submit" 
        class="btn w-full"
        :disabled="loading"
      >
        {{ loading ? '注册中...' : '注册' }}
      </button>
      
      <div class="text-center">
        <router-link to="/login" class="text-blue-500 hover:text-blue-700">
          已有账号？立即登录
        </router-link>
      </div>
    </form>
    
    <!-- 错误提示 -->
    <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
      {{ error }}
    </div>
    
    <!-- 成功提示 -->
    <div v-if="success" class="mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
      {{ success }}
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'

export default {
  name: 'RegisterView',
  setup() {
    const router = useRouter()
    const store = useStore()
    
    const form = ref({
      username: '',
      password: '',
      confirmPassword: ''
    })
    
    const loading = ref(false)
    const error = ref('')
    const success = ref('')
    
    const register = async () => {
      error.value = ''
      success.value = ''
      
      // 验证表单
      if (!form.value.username || !form.value.password || !form.value.confirmPassword) {
        error.value = '请填写所有字段'
        return
      }
      
      if (form.value.password !== form.value.confirmPassword) {
        error.value = '两次输入的密码不一致'
        return
      }
      
      if (form.value.password.length < 6) {
        error.value = '密码长度至少6位'
        return
      }
      
      loading.value = true
      
      try {
        await store.dispatch('register', {
          username: form.value.username,
          password: form.value.password
        })
        
        success.value = '注册成功！3秒后跳转到登录页面...'
        
        setTimeout(() => {
          router.push('/login')
        }, 3000)
        
      } catch (err) {
        error.value = err || '注册失败，请重试'
      } finally {
        loading.value = false
      }
    }
    
    return {
      form,
      loading,
      error,
      success,
      register
    }
  }
}
</script>

<style scoped>
.input-field {
  @apply border border-gray-300 rounded-lg p-3 w-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.btn {
  @apply py-3 px-4 rounded-lg text-white bg-blue-500 hover:bg-blue-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
