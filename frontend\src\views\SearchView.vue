<template>
  <div class="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-4">
    <div class="max-w-md mx-auto">
      <!-- 头部 -->
      <div class="text-center mb-8">
        <div class="text-6xl mb-4">🔍</div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">找留言</h1>
        <p class="text-gray-600">输入名字和暗号查找专属留言</p>
      </div>

      <!-- 搜索表单 -->
      <div class="bg-white rounded-2xl shadow-xl p-6 mb-6">
        <form @submit.prevent="searchMessage" class="space-y-4">
          <div>
            <label class="block mb-2 text-sm font-medium text-gray-700">
              收件人名字 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              class="input-field"
              placeholder="请输入你的名字"
              required
            >
          </div>

          <div>
            <label class="block mb-2 text-sm font-medium text-gray-700">
              暗号 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.code"
              type="text"
              class="input-field"
              placeholder="请输入暗号"
              required
            >
          </div>

          <button
            type="submit"
            class="btn w-full"
            :disabled="loading"
          >
            {{ loading ? '搜索中...' : '🔍 搜索留言' }}
          </button>
        </form>

        <!-- 错误提示 -->
        <div v-if="error" class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg">
          {{ error }}
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="messages.length > 0" class="space-y-4">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">找到 {{ messages.length }} 条留言</h2>

        <div
          v-for="(message, index) in messages"
          :key="message.id"
          class="bg-white rounded-xl shadow-lg p-6"
        >
          <div class="flex items-start space-x-4 mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold">
              {{ getInitial(message.sender_name) }}
            </div>
            <div class="flex-1">
              <div class="flex items-center space-x-2 mb-1">
                <span class="font-medium text-gray-800">{{ message.sender_name }}</span>
                <span class="text-xs text-gray-500">{{ formatDate(message.created_at) }}</span>
              </div>
              <div class="text-xs text-gray-500">
                给 {{ message.receiver_name }} 的留言
              </div>
            </div>
          </div>

          <div class="bg-gray-50 rounded-lg p-4 mb-4">
            <p class="text-gray-800 leading-relaxed">{{ message.content }}</p>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button
                @click="likeMessage(message.id)"
                class="flex items-center space-x-1 text-gray-500 hover:text-red-500 transition-colors"
              >
                <span class="text-sm">❤️</span>
                <span class="text-sm">{{ message.likes || 0 }}</span>
              </button>
              <span class="text-xs text-gray-500">
                {{ message.is_public ? '公开留言' : '私密留言' }}
              </span>
            </div>
            <span class="text-xs text-gray-500">
              #{{ index + 1 }}
            </span>
          </div>
        </div>
      </div>

      <!-- 无结果提示 -->
      <div v-else-if="searched && !loading" class="bg-white rounded-xl shadow-lg p-8 text-center">
        <div class="text-6xl mb-4">📭</div>
        <h3 class="text-lg font-semibold text-gray-800 mb-2">没有找到留言</h3>
        <p class="text-gray-600 mb-4">请检查名字和暗号是否正确</p>
        <div class="text-sm text-gray-500">
          <p>💡 小提示：</p>
          <p>• 名字和暗号需要完全匹配</p>
          <p>• 区分大小写</p>
          <p>• 确认发送者是否已经发送留言</p>
        </div>
      </div>

      <!-- 使用说明 -->
      <div v-if="!searched" class="bg-white rounded-xl shadow-lg p-6 mt-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-3">📖 使用说明</h3>
        <div class="space-y-2 text-sm text-gray-600">
          <p>• 输入你的名字（收件人名字）</p>
          <p>• 输入发送者告诉你的暗号</p>
          <p>• 点击搜索查看专属留言</p>
          <p>• 一个暗号可能对应多条留言</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import api from '@/api'

export default {
  name: 'SearchView',
  setup() {
    const form = ref({
      name: '',
      code: ''
    })

    const messages = ref([])
    const loading = ref(false)
    const error = ref('')
    const searched = ref(false)

    const searchMessage = async () => {
      error.value = ''

      if (!form.value.name || !form.value.code) {
        error.value = '请填写名字和暗号'
        return
      }

      loading.value = true
      searched.value = true

      try {
        const response = await api.messages.search(form.value.name, form.value.code)

        // 如果返回的是单个对象，转换为数组
        if (response.data && !Array.isArray(response.data)) {
          messages.value = [response.data]
        } else {
          messages.value = response.data || []
        }

      } catch (err) {
        if (err.response?.status === 404) {
          messages.value = []
        } else {
          error.value = err.response?.data?.error || '搜索失败，请重试'
        }
      } finally {
        loading.value = false
      }
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now.getTime() - date.getTime()

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

      return date.toLocaleDateString('zh-CN')
    }

    const getInitial = (name) => {
      return name ? name.charAt(0).toUpperCase() : 'U'
    }

    const likeMessage = async (messageId) => {
      try {
        await api.post(`/messages/${messageId}/like`)
        // 更新本地点赞数
        const message = messages.value.find(m => m.id === messageId)
        if (message) {
          message.likes = (message.likes || 0) + 1
        }
      } catch (error) {
        console.error('点赞失败:', error)
      }
    }

    return {
      form,
      messages,
      loading,
      error,
      searched,
      searchMessage,
      formatDate,
      getInitial,
      likeMessage
    }
  }
}
</script>

<style scoped>
.input-field {
  @apply border border-gray-300 rounded-lg p-3 w-full focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all;
}

.btn {
  @apply py-3 px-4 rounded-lg text-white bg-purple-500 hover:bg-purple-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
